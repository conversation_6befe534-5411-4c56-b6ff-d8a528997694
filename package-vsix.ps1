# VSCode Extension Packaging Script - Package directory contents as .vsix file
# Author: AI Assistant
# Function: Compress specified directory to ZIP format, then rename to .vsix extension

param(
    [Parameter(Mandatory=$false, HelpMessage="Source directory path containing the extension files")]
    [string]$SourcePath = "D:\augment-pojie\0.521\vscode-augment-0.521.1-yuanban",

    [Parameter(Mandatory=$false, HelpMessage="Output directory path")]
    [string]$OutputPath = "D:\augment-pojie\0.521",

    [Parameter(Mandatory=$false, HelpMessage="Output file name (without extension)")]
    [string]$OutputName = "vscode-augment-0.521.1-repackaged"
)

# Set encoding to UTF-8 for proper display
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 主函数
function Package-VSCodeExtension {
    try {
        Write-ColorOutput "=== VSCode插件打包工具 ===" "Cyan"
        Write-ColorOutput "开始打包VSCode插件..." "Green"
        
        # 验证源目录是否存在
        if (-not (Test-Path $SourcePath)) {
            throw "源目录不存在: $SourcePath"
        }
        
        Write-ColorOutput "源目录: $SourcePath" "Yellow"
        
        # 验证源目录是否包含必要的VSCode插件文件
        $requiredFiles = @("extension.vsixmanifest", "[Content_Types].xml")
        foreach ($file in $requiredFiles) {
            $filePath = Join-Path $SourcePath $file
            if (-not (Test-Path $filePath)) {
                Write-ColorOutput "警告: 未找到必需文件 $file，但继续打包..." "Yellow"
            }
        }
        
        # 确保输出目录存在
        if (-not (Test-Path $OutputPath)) {
            Write-ColorOutput "创建输出目录: $OutputPath" "Yellow"
            New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
        }
        
        # 生成输出文件路径
        $zipPath = Join-Path $OutputPath "$OutputName.zip"
        $vsixPath = Join-Path $OutputPath "$OutputName.vsix"
        
        # 删除已存在的文件
        if (Test-Path $zipPath) {
            Write-ColorOutput "删除已存在的ZIP文件..." "Yellow"
            Remove-Item $zipPath -Force
        }
        
        if (Test-Path $vsixPath) {
            Write-ColorOutput "删除已存在的VSIX文件..." "Yellow"
            Remove-Item $vsixPath -Force
        }
        
        Write-ColorOutput "正在压缩文件..." "Green"
        
        # 使用.NET的ZipFile类进行压缩（更可靠）
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        
        # 创建ZIP文件
        [System.IO.Compression.ZipFile]::CreateFromDirectory($SourcePath, $zipPath, [System.IO.Compression.CompressionLevel]::Optimal, $false)
        
        # 验证ZIP文件是否创建成功
        if (-not (Test-Path $zipPath)) {
            throw "ZIP文件创建失败"
        }
        
        $zipSize = (Get-Item $zipPath).Length
        Write-ColorOutput "ZIP文件创建成功，大小: $([math]::Round($zipSize/1MB, 2)) MB" "Green"
        
        # 重命名为.vsix文件
        Write-ColorOutput "重命名为VSIX格式..." "Green"
        Rename-Item $zipPath $vsixPath
        
        # 验证最终文件
        if (-not (Test-Path $vsixPath)) {
            throw "VSIX文件重命名失败"
        }
        
        $finalSize = (Get-Item $vsixPath).Length
        Write-ColorOutput "✅ 打包完成!" "Green"
        Write-ColorOutput "输出文件: $vsixPath" "Cyan"
        Write-ColorOutput "文件大小: $([math]::Round($finalSize/1MB, 2)) MB" "Cyan"
        
        # 显示文件内容概览
        Write-ColorOutput "`n📦 打包内容概览:" "Yellow"
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        $zip = [System.IO.Compression.ZipFile]::OpenRead($vsixPath)
        $fileCount = $zip.Entries.Count
        $zip.Dispose()
        
        Write-ColorOutput "总文件数: $fileCount" "White"
        
        # 验证关键文件是否存在于压缩包中
        $zip = [System.IO.Compression.ZipFile]::OpenRead($vsixPath)
        $hasManifest = $zip.Entries | Where-Object { $_.Name -eq "extension.vsixmanifest" }
        $hasContentTypes = $zip.Entries | Where-Object { $_.Name -eq "[Content_Types].xml" }
        $zip.Dispose()
        
        if ($hasManifest) {
            Write-ColorOutput "✅ extension.vsixmanifest 已包含" "Green"
        } else {
            Write-ColorOutput "⚠️  extension.vsixmanifest 未找到" "Yellow"
        }
        
        if ($hasContentTypes) {
            Write-ColorOutput "✅ [Content_Types].xml 已包含" "Green"
        } else {
            Write-ColorOutput "⚠️  [Content_Types].xml 未找到" "Yellow"
        }
        
        Write-ColorOutput "`n🎉 VSCode插件打包成功完成!" "Green"
        Write-ColorOutput "您可以使用以下命令安装插件:" "Cyan"
        Write-ColorOutput "code --install-extension `"$vsixPath`"" "White"
        
        return $vsixPath
        
    } catch {
        Write-ColorOutput "❌ 打包失败: $($_.Exception.Message)" "Red"
        Write-ColorOutput "错误详情: $($_.Exception.ToString())" "Red"
        
        # 清理临时文件
        if (Test-Path $zipPath) {
            try {
                Remove-Item $zipPath -Force
                Write-ColorOutput "已清理临时ZIP文件" "Yellow"
            } catch {
                Write-ColorOutput "清理临时文件失败: $($_.Exception.Message)" "Yellow"
            }
        }
        
        throw
    }
}

# 脚本入口点
if ($MyInvocation.InvocationName -ne '.') {
    try {
        Write-ColorOutput "开始执行VSCode插件打包..." "Cyan"
        $result = Package-VSCodeExtension
        Write-ColorOutput "脚本执行完成，输出文件: $result" "Green"
        exit 0
    } catch {
        Write-ColorOutput "脚本执行失败: $($_.Exception.Message)" "Red"
        exit 1
    }
}
